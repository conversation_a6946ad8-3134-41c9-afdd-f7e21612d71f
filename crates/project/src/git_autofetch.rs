use crate::{
    git_store::{GitStore, GitStoreState},
    project_settings::{AutofetchSetting, ProjectSettings},
};
use askpass::AskPassDelegate;
use git::repository::{FetchOptions, Remote};
use gpui::{App, Entity, Global, Task, WeakEntity};
use settings::{Settings, SettingsStore};

/// Global autofetch service that manages git autofetch for all repositories
/// without causing borrowing conflicts with the GitStore.
pub struct GitAutofetchService {
    git_stores: Vec<WeakEntity<GitStore>>,
    timer_task: Option<Task<()>>,
}

impl Global for GitAutofetchService {}

impl GitAutofetchService {
    pub fn init(cx: &mut App) {
        let service = Self {
            git_stores: Vec::new(),
            timer_task: None,
        };
        cx.set_global(service);

        // Observe settings changes to restart autofetch
        cx.observe_global::<SettingsStore>(|cx| {
            cx.update_global::<GitAutofetchService, _>(|service, cx| {
                service.restart_autofetch(cx);
            });
        })
        .detach();
    }

    pub fn register_git_store(&mut self, git_store: WeakEntity<GitStore>, cx: &mut App) {
        self.git_stores.push(git_store);
        self.restart_autofetch(cx);
    }

    pub fn unregister_git_store(&mut self, git_store: &WeakEntity<GitStore>, cx: &mut App) {
        self.git_stores.retain(|store| !store.ptr_eq(git_store));
        if self.git_stores.is_empty() {
            self.stop_autofetch();
        } else {
            self.restart_autofetch(cx);
        }
    }

    fn restart_autofetch(&mut self, cx: &mut App) {
        self.stop_autofetch();
        self.start_autofetch(cx);
    }

    fn stop_autofetch(&mut self) {
        self.timer_task = None;
    }

    fn start_autofetch(&mut self, cx: &mut App) {
        // Clean up dead git stores
        self.git_stores.retain(|store| store.upgrade().is_some());

        if self.git_stores.is_empty() {
            return;
        }

        // Check if any git store has autofetch enabled
        let mut min_interval = None;
        for git_store_weak in &self.git_stores {
            if let Some(git_store) = git_store_weak.upgrade() {
                let git_store = git_store.read(cx);

                // Skip remote git stores
                if matches!(git_store.state, GitStoreState::Remote { .. }) {
                    continue;
                }

                for repository in git_store.repositories().values() {
                    let repo = repository.read(cx);
                    let settings_location = repo.get_settings_location(cx);
                    let settings = ProjectSettings::get(settings_location, cx);

                    if settings.git.autofetch_enabled() {
                        let interval = settings.git.autofetch_period();
                        min_interval = Some(min_interval.map_or(interval, |min| min.min(interval)));
                    }
                }
            }
        }

        let Some(interval) = min_interval else {
            return;
        };

        log::info!(
            "Starting global autofetch service with interval: {}s - TODO: Remove this log",
            interval.as_secs()
        );

        let git_stores = self.git_stores.clone();
        self.timer_task = Some(cx.spawn(async move |cx| {
            loop {
                cx.background_executor().timer(interval).await;

                // Check if we should continue and schedule autofetch jobs
                let should_continue = cx.update(|cx| {
                    let mut any_enabled = false;

                    for git_store_weak in &git_stores {
                        if let Some(git_store) = git_store_weak.upgrade() {
                            let git_store = git_store.read(cx);

                            // Skip remote git stores
                            if matches!(git_store.state, GitStoreState::Remote { .. }) {
                                continue;
                            }

                            for repository in git_store.repositories().values() {
                                let repo = repository.read(cx);
                                let settings_location = repo.get_settings_location(cx);
                                let settings = ProjectSettings::get(settings_location, cx);

                                if settings.git.autofetch_enabled() {
                                    any_enabled = true;

                                    // Schedule autofetch for this repository
                                    let autofetch_setting = settings.git.autofetch_setting();
                                    Self::schedule_autofetch_for_repository(repository.clone(), autofetch_setting, cx);
                                }
                            }
                        }
                    }

                    any_enabled
                }).unwrap_or(false);

                if !should_continue {
                    break;
                }
            }
        }));
    }

    fn schedule_autofetch_for_repository(
        repository: Entity<crate::git_store::Repository>,
        autofetch_setting: AutofetchSetting,
        cx: &mut App,
    ) {
        repository.update(cx, |repo, _cx| {
            let fetch_options = match autofetch_setting {
                AutofetchSetting::Default => {
                    if let Some(branch) = &repo.branch {
                        if let Some(upstream) = &branch.upstream {
                            if let Some(remote_name) = upstream.remote_name() {
                                FetchOptions::Remote(Remote {
                                    name: remote_name.to_string().into(),
                                })
                            } else {
                                FetchOptions::Remote(Remote {
                                    name: "origin".into(),
                                })
                            }
                        } else {
                            FetchOptions::Remote(Remote {
                                name: "origin".into(),
                            })
                        }
                    } else {
                        FetchOptions::Remote(Remote {
                            name: "origin".into(),
                        })
                    }
                }
                AutofetchSetting::All => FetchOptions::All,
                AutofetchSetting::Off => return,
            };

            log::info!(
                "Scheduling autofetch for repository: {} with options: {:?} - TODO: Remove this log",
                repo.work_directory_abs_path.display(),
                fetch_options
            );

            let _ = repo.send_job(
                Some("autofetch".into()),
                move |state, cx| async move {
                    match state {
                        crate::git_store::RepositoryState::Local { backend, environment } => {
                            let askpass = AskPassDelegate::new(&mut cx.clone(), |_prompt, tx, _cx| {
                                let _ = tx.send(String::new());
                            });
                            let _ = backend.fetch(fetch_options, askpass, environment, cx).await;
                        }
                        crate::git_store::RepositoryState::Remote { .. } => {
                            // Remote repositories don't support autofetch
                        }
                    }
                },
            );
        });
    }
}
