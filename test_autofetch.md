# Autofetch Refactor Test Plan

## Changes Made

1. **Replaced direct git operations with job-based approach**
   - Autofetch now schedules jobs instead of performing git operations directly
   - This prevents borrowing conflicts with the main thread

2. **Added GitJobKey::Autofetch**
   - Ensures autofetch operations are properly queued and deduplicated
   - Prevents multiple autofetch operations from running simultaneously

3. **Fixed borrowing conflicts in restart_autofetch_for_all_repositories**
   - Collects repository handles before iterating to avoid borrowing conflicts

## Testing Steps

### 1. Basic Functionality Test
- Open Zed with a git repository
- Enable autofetch in settings
- Verify that autofetch starts without panics
- Check logs for autofetch activity

### 2. Settings Change Test
- Change autofetch settings while <PERSON><PERSON> is running
- Verify that autofetch restarts without causing panics
- Test different autofetch modes (default, all, off)

### 3. Multiple Repository Test
- Open multiple git repositories
- Verify that each repository gets its own autofetch timer
- Change settings and ensure all repositories restart autofetch

### 4. Panic Prevention Test
- Rapidly change autofetch settings
- Open/close multiple repositories
- Verify no "cannot read project::git_store::GitStore while it is already being updated" panics

## Expected Behavior

- Autofetch should work exactly as before
- No borrowing conflict panics
- All git operations happen in background threads
- Settings changes should be respected immediately
- Multiple repositories should work independently

## Key Architecture Changes

- **Timer Task**: Only schedules jobs, doesn't perform git operations
- **Job System**: All git operations go through the existing job queue
- **Background Execution**: Git fetch happens in worker threads
- **No Main Thread Blocking**: Settings changes don't block on git operations
