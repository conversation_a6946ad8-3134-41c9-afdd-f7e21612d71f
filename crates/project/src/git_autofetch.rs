use crate::{
    git_store::{GitStore, GitStoreState},
    project_settings::AutofetchSetting,
};
use askpass::AskPassDelegate;
use git::repository::{FetchOptions, Remote};
use gpui::{App, BorrowAppContext, Entity, Global, Task, WeakEntity};
use settings::{Settings, SettingsStore};

/// Global autofetch service that manages git autofetch for all repositories
/// without causing borrowing conflicts with the GitStore.
pub struct GitAutofetchService {
    git_stores: Vec<WeakEntity<GitStore>>,
    timer_task: Option<Task<()>>,
}

impl Global for GitAutofetchService {}

impl GitAutofetchService {
    pub fn init(cx: &mut App) {
        let service = Self {
            git_stores: Vec::new(),
            timer_task: None,
        };
        cx.set_global(service);

        // Observe settings changes to restart autofetch
        cx.observe_global::<SettingsStore>(|cx| {
            cx.update_global::<GitAutofetchService, _>(|service, cx| {
                service.restart_autofetch(cx);
            });
        })
        .detach();
    }

    pub fn register_git_store(&mut self, git_store: WeakEntity<GitStore>, cx: &mut App) {
        self.git_stores.push(git_store);
        self.restart_autofetch(cx);
    }

    pub fn unregister_git_store(&mut self, git_store: &WeakEntity<GitStore>, cx: &mut App) {
        self.git_stores.retain(|store| {
            // Compare by checking if they upgrade to the same entity
            match (store.upgrade(), git_store.upgrade()) {
                (Some(a), Some(b)) => !a.entity_id().eq(&b.entity_id()),
                _ => false, // Remove dead weak references
            }
        });
        if self.git_stores.is_empty() {
            self.stop_autofetch();
        } else {
            self.restart_autofetch(cx);
        }
    }

    fn restart_autofetch(&mut self, cx: &mut App) {
        self.stop_autofetch();
        self.start_autofetch(cx);
    }

    fn stop_autofetch(&mut self) {
        self.timer_task = None;
    }

    fn start_autofetch(&mut self, cx: &mut App) {
        // Clean up dead git stores
        self.git_stores.retain(|store| store.upgrade().is_some());

        if self.git_stores.is_empty() {
            return;
        }

        // Use a fixed interval and check settings during each tick
        let interval = std::time::Duration::from_secs(60); // Check every minute

        log::info!(
            "Starting global autofetch service with interval: {}s - TODO: Remove this log",
            interval.as_secs()
        );

        let git_stores = self.git_stores.clone();
        self.timer_task = Some(cx.spawn(async move |cx| {
            loop {
                cx.background_executor().timer(interval).await;

                // Check if we should continue and schedule autofetch jobs
                let should_continue = cx.update(|cx| {
                    let mut any_enabled = false;
                    let mut repositories_to_fetch = Vec::new();

                    // First, collect all repositories that need autofetch
                    for git_store_weak in &git_stores {
                        if let Some(git_store) = git_store_weak.upgrade() {
                            let git_store = git_store.read(cx);

                            // Skip remote git stores
                            if matches!(git_store.state, GitStoreState::Remote { .. }) {
                                continue;
                            }

                            for repository in git_store.repositories().values() {
                                repositories_to_fetch.push(repository.clone());
                                any_enabled = true;
                            }
                        }
                    }

                    // Now schedule autofetch for each repository
                    for repository in repositories_to_fetch {
                        Self::schedule_autofetch_for_repository(repository, cx);
                    }

                    any_enabled
                }).unwrap_or(false);

                if !should_continue {
                    break;
                }
            }
        }));
    }

    fn schedule_autofetch_for_repository(
        repository: Entity<crate::git_store::Repository>,
        cx: &mut App,
    ) {
        repository.update(cx, |repo, cx| {
            // Check settings within the repository context
            let settings_location = repo.get_settings_location(cx);
            let settings = crate::project_settings::ProjectSettings::get(settings_location, cx);

            if !settings.git.autofetch_enabled() {
                return;
            }

            let autofetch_setting = settings.git.autofetch_setting();
            let fetch_options = match autofetch_setting {
                AutofetchSetting::Default => {
                    if let Some(branch) = &repo.branch {
                        if let Some(upstream) = &branch.upstream {
                            if let Some(remote_name) = upstream.remote_name() {
                                FetchOptions::Remote(Remote {
                                    name: remote_name.to_string().into(),
                                })
                            } else {
                                FetchOptions::Remote(Remote {
                                    name: "origin".into(),
                                })
                            }
                        } else {
                            FetchOptions::Remote(Remote {
                                name: "origin".into(),
                            })
                        }
                    } else {
                        FetchOptions::Remote(Remote {
                            name: "origin".into(),
                        })
                    }
                }
                AutofetchSetting::All => FetchOptions::All,
                AutofetchSetting::Off => return,
            };

            log::info!(
                "Scheduling autofetch for repository: {} with options: {:?} - TODO: Remove this log",
                repo.work_directory_abs_path.display(),
                fetch_options
            );

            let _ = repo.send_job(
                Some("autofetch".into()),
                move |state, cx| async move {
                    match state {
                        crate::git_store::RepositoryState::Local { backend, environment } => {
                            let askpass = AskPassDelegate::new(&mut cx.clone(), |_prompt, tx, _cx| {
                                let _ = tx.send(String::new());
                            });
                            let _ = backend.fetch(fetch_options, askpass, environment, cx).await;
                        }
                        crate::git_store::RepositoryState::Remote { .. } => {
                            // Remote repositories don't support autofetch
                        }
                    }
                },
            );
        });
    }
}
